import os
from openpyxl import load_workbook
from venv import logger
import openai
import pandas as pd
import time

import pop_up_window_functions_V2 as popup
import LLM_Interfacing as llm
import file_editing as fe



if __name__ == "__main__":
    #Dan's LM Studio base_url
    # LLM_base_url = "http://***************:1234/v1"
    # LLM_model = "qwen/qwen3-235b-a22b-2507"

    LLM_base_url = "http://127.0.0.1:1234/v1"
    LLM_model = "qwen/qwen3-coder-30b"

    client = openai.OpenAI(
        # My LM Studio base_url 
        base_url=LLM_base_url,
        api_key="lm-studio" # The API key is not required for LM Studio, but this value is a common placeholder.
    )

    # Get the paths from the popup
    PATHS = popup.make_window()

    # Extract the paths from the dictionary
    job_description_or_requirements_file = PATHS['job_description_or_requirements_file']
    resume_folder = PATHS['resume_folder']
    output_file_folder = PATHS['output_folder']
    HR_data_file = PATHS['master_reference_file']
    output_file = PATHS['existing_csv_file_or_new_file_name']
    has_requirements_file = PATHS['Has_Requirements_File']
    has_existing_csv = PATHS['Has_Existing_CSV']



    # print(job_description_or_requirements_file)
    # Dictionary to store resume analysis results
    resume_dic = {}

    # Read the job description and extract the requirements using LLM
    job_description = ""

    if has_requirements_file:
        try:
            with open(job_description_or_requirements_file, 'r') as file:
                job_description = file.read()
        except FileNotFoundError:
            print(f"Error: The file '{job_description_or_requirements_file}' was not found.")
            exit()

    else:
        job_description = llm.get_job_requirements(job_description_or_requirements_file, client, LLM_model)
        with open(f'{output_file_folder}/job_requirements.txt', 'w') as file:
            file.write(job_description)

    print(job_description)


    # Obtain the start time to calculate the total time taken
    start_time = time.time()

    resumes = os.listdir(resume_folder)

    if has_existing_csv and os.path.exists(output_file):
        past_resumes_analysis_info = pd.read_csv(output_file)
        past_resumes = past_resumes_analysis_info['FILE NAME'].tolist()
        print(f"Past resumes: {past_resumes}")
        resumes = [resume for resume in resumes if resume not in past_resumes]

    print("Resumes to be processed:")
    print(resumes)

    print(len(resumes))

    resume_dic = past_resumes_analysis_info.to_dict('index')

    # Loops through all resumes in the folder
    for filename in resumes:

        # Ignore the .DS_Store file if it exists
        if filename == '.DS_Store':
            continue

        # Get the full path to the resume file
        resume_path = os.path.join(resume_folder, filename)

        # Extract text from the resume file
        resume_text = fe.get_text_from_file(resume_path)
        if resume_text:
            resume_text = f'{filename} {resume_text}'  # Prepend filename to the text for context

            # Analyze the resume text using LLM
            result = llm.analyze_text_with_llm(resume_text, job_description, client, LLM_model)
            # Check if the result is a dictionary before proceeding
            if isinstance(result, dict):
                # Store the result in the dictionary
                resume_dic[filename] = result
                print('\n\n')
                print(f"Result for {filename}: {result}")
                print('\n\n')
                # Convert the dictionary to a DataFrame
                analysis_df = pd.DataFrame.from_dict(resume_dic, orient='index').reset_index(drop=True)
                # Write the DataFrame to a CSV file
                analysis_df.to_csv(output_file, index=False)
            else:
                print(f"Unexpected result format for {filename}: {result}")
        else:
            print(f"Unsupported file type or error reading file: {filename}")
    end_time = time.time()

    time_taken = (end_time - start_time) / 60
    print(f"Total time taken: {time_taken} minutes")

    # Convert the dictionary to a DataFrame
    analysis_df = pd.DataFrame.from_dict(resume_dic, orient='index').reset_index(drop=True)

    # Write the DataFrame to a CSV file
    analysis_df.to_csv(output_file, index=False)

    # Merge the analysis results with the HR data
    fe.merge_data_to_excel(HR_data_file, 'Master Data', analysis_df, f'{output_file_folder}/{output_file_name}.xlsx')
