
import time
import tkinter as tk
from tkinter import filedialog, ttk# --- GUI Helper Functions ---

PATHS = {
    'job_description_or_requirements_file': '',
    'master_reference_file': '',
    'resume_folder': '',
    'output_folder': '',
    'output_file_name': '',
    'Has_Requirements_File': '',
    'isDone': False
}

# Function to select a single file path.
def select_file(entry_widget):
    """Opens a file dialog and updates the specified Entry widget with the selected file path."""
    path = filedialog.askopenfilename(title="Select File")
    if path:
        # Clear existing text and insert new path
        entry_widget.delete(0, tk.END) 
        entry_widget.insert(0, path)

# Function to select a folder path.
def select_folder(entry_widget):
    """Opens a folder dialog and updates the specified Entry widget with the selected folder path."""
    path = filedialog.askdirectory(title="Select Folder")
    if path:
        # Clear existing text and insert new path
        entry_widget.delete(0, tk.END)
        entry_widget.insert(0, path)

# --- Data Handling and GUI Exit ---

# Function called when the OK button is pressed to retrieve data and close the GUI.
def get_paths(root, job_description_or_requirements_file, master_reference_file, resume_folder, output_folder, output_file_name):
    """
    Retrieves paths from the entry widgets, stores them in the global PATHS dictionary, 
    sets the 'isDone' flag to True, and signals the manual event loop to exit.
    """

    # 1. Secure all data before signaling the exit.
    PATHS['job_description_or_requirements_file'] = job_description_or_requirements_file.get()
    PATHS['master_reference_file'] = master_reference_file.get()
    PATHS['resume_folder'] = resume_folder.get()
    PATHS['output_folder'] = output_folder.get()
    PATHS['output_file_name'] = output_file_name.get()
    
    # 2. Set the flag to exit the manual loop.
    PATHS['isDone'] = True
    
    # 3. Exit the Tkinter main loop and hide the window.
    # root.quit() stops any existing mainloop, and withdraw() hides the window.
    root.quit()
    root.withdraw()



def toggle_file_selection(job_description_or_requirements_file, BUTTON_VAR, conditional_label, conditional_frame):
    """Manages the visibility and text of the conditional file selection frame."""

    choice = BUTTON_VAR.get()
    
    # Determine the label text based on the radio button value (1 for Yes, 0 for No)
    if choice == 1:
        # Yes: User has the requirements file
        label_text = "Please select the job requirements file:"
        PATHS['Has_Requirements_File'] = True
    elif choice == 0:
        # No: User needs to select the job description
        label_text = "Please select the job description file:"
        PATHS['Has_Requirements_File'] = False
    else:
        # Should not be reached if initial value is set, but good practice
        conditional_frame.grid_forget()
        return

    # 1. Update the dynamic label's text
    conditional_label.set(label_text)
    
    # 2. Make the conditional frame visible using grid()
    conditional_frame.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky='w')
    
    # 3. Clear any previous file path in the Entry widget for clarity
    job_description_or_requirements_file.delete(0, tk.END)

# --- Main Window Creation and Manual Event Loop ---


# Function to create and run the file selection GUI.
def make_window():

    job_description_or_requirements_file = None
    button_var = None
    conditional_label = None
    conditional_frame = None
    # Initialize the main Tkinter window.
    root = tk.Tk()
    root.title("Resume Analyzer File and Folder Selection")

    # --- Widget Creation and Layout ---

    # Initialize the Tkinter control variables
    button_var = tk.IntVar(root, value=-1) # Holds 1 (Yes) or 0 (No)
    conditional_label = tk.StringVar(root, value="")

    # --- Row 0: Question and Radio Buttons ---
    
    tk.Label(root, text="Do you already have a job requirements file from a previous run?").grid(row=0, column=0, padx=5, pady=5, sticky='w')

    # Radio Button for YES
    tk.Radiobutton(root,
                   text="Yes",
                   variable=button_var,
                   value=1,
                   command=lambda: toggle_file_selection(job_description_or_requirements_file, button_var, conditional_label, conditional_frame)).grid(row=0, column=1, padx=2, pady=5, sticky='w')

    # Radio Button for NO
    tk.Radiobutton(root,
                   text="No",
                   variable=button_var,
                   value=0,
                   command=lambda: toggle_file_selection(job_description_or_requirements_file, button_var, conditional_label, conditional_frame)).grid(row=0, column=2, padx=2, pady=5, sticky='w')

    # --- Row 1: The Conditional File Selection Frame (Starts Hidden) ---
    
    conditional_frame = tk.Frame(root)
    # Note: We do NOT call grid() yet, the frame starts hidden.

    # Dynamic Label (uses conditional_label)
    dynamic_label = tk.Label(conditional_frame, textvariable=conditional_label)
    dynamic_label.grid(row=0, column=0, padx=5, pady=5, sticky='w')


    # Entry widget for the file path
    job_description_or_requirements_file = tk.Entry(conditional_frame, width=50)
    job_description_or_requirements_file.grid(row=0, column=1, padx=5, pady=5)
    tk.Button(conditional_frame, text="Browse...", command=lambda: select_file(job_description_or_requirements_file)).grid(row=0, column=2, padx=5, pady=5)


    # Row 1: Job Requirements File
    # tk.Label(root, text="If you already have a job requirements file from a previous run, please select it, if not leave blank:").grid(row=1, column=0, padx=5, pady=5, sticky='w')
    # job_requirements_file = tk.Entry(root, width=50)
    # job_requirements_file.grid(row=1, column=1, padx=5, pady=5)
    # tk.Button(root, text="Browse...", command=lambda: select_file(job_requirements_file)).grid(row=1, column=2, padx=5, pady=5)

    # Row 2: Master Reference HR File
    tk.Label(root, text="Please select the master reference HR file:").grid(row=2, column=0, padx=5, pady=5, sticky='w')
    master_reference_file = tk.Entry(root, width=50)
    master_reference_file.grid(row=2, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_file(master_reference_file)).grid(row=2, column=2, padx=5, pady=5)    

    # Row 3: Resume Folder
    tk.Label(root, text="Please select the folder containing the resumes:").grid(row=3, column=0, padx=5, pady=5, sticky='w')
    resume_folder = tk.Entry(root, width=50)
    resume_folder.grid(row=3, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_folder(resume_folder)).grid(row=3, column=2, padx=5, pady=5)

    # Row 4: Output Folder
    tk.Label(root, text="Please select the folder in which to store the output:").grid(row=4, column=0, padx=5, pady=5, sticky='w')
    output_folder = tk.Entry(root, width=50)
    output_folder.grid(row=4, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_folder(output_folder)).grid(row=4, column=2, padx=5, pady=5)

    # Row 5: Output File Name (Note: Corrected spelling from 'ouput_file_name')
    tk.Label(root, text="Please enter the name of the output file:").grid(row=5, column=0, padx=5, pady=5, sticky='w')
    output_file_name = tk.Entry(root, width=50)
    output_file_name.grid(row=5, column=1, padx=5, pady=5)

    # Row 6: OK Button
    btn_ok = tk.Button(root, text="OK", command=lambda: get_paths(root, job_description_or_requirements_file, master_reference_file, resume_folder, output_folder, output_file_name), width=10)
    btn_ok.grid(row=6, column=1, pady=10)
    # --- Manual Event Loop (Non-Blocking) ---
    
    # This loop replaces root.mainloop() and is crucial for keeping the window 
    # responsive while the program waits for the user to click OK.
    while PATHS['isDone'] == False:
        # Process all pending events (e.g., button clicks, window movement).
        root.update() 
        # Pause briefly to yield CPU time, preventing excessive resource usage.
        time.sleep(0.01)

    # After the loop exits (PATHS['isDone'] is True), explicitly destroy the Tkinter instance.
    root.destroy()

    return PATHS





