# Resume Analyzer Program Architecture

## Overview
The Resume Analyzer is a Python application that processes job descriptions and resumes using an LLM (Large Language Model) to evaluate candidate qualifications. The system integrates file handling, GUI interaction, LLM interfacing, and data processing to generate structured analysis results.

## Component Diagram

```mermaid
graph TD
    A[User] --> B[GUI Window]
    B --> C[File Selection]
    C --> D[resumeAnalyzer.py]
    D --> E[LLM_Interfacing.py]
    E --> F[Local LLM Server]
    F --> E
    E --> G[Analysis Results]
    D --> H[file_editing.py]
    H --> I[Resume Text Extraction]
    H --> J[Excel Output]
    D --> K[pop_up_window_functions.py]
    K --> B
    G --> H
    H --> J
```

## Components

### [`pop_up_window_functions.py`](pop_up_window_functions.py:1)
- Provides a Tkinter-based GUI for file and folder selection
- Handles user input for job description, resume folder, output location, and HR reference file
- Returns a dictionary of selected paths to the main program

### [`resumeAnalyzer.py`](resumeAnalyzer.py:1)
- Main entry point and orchestrator of the entire process
- Initializes LLM client connection to local server
- Coordinates between GUI, LLM interfacing, and file editing modules
- Manages the workflow from input collection to final output generation

### [`LLM_Interfacing.py`](LLM_Interfacing.py:1)
- Interfaces with the local LLM server (LM Studio) via OpenAI-compatible API
- Contains two primary functions:
  - `analyze_text_with_llm()`: Analyzes resume text against job requirements using strict vetting protocol
  - `get_job_requirements()`: Extracts structured job requirements from job description document
- Implements deterministic skepticism engine with hierarchical analysis rules

### [`file_editing.py`](file_editing.py:1)
- Handles file format conversion and text extraction
- Supports .docx and .pdf resume formats via docx2txt and pdfplumber
- Merges analysis results with HR data in Excel format
- Creates output files in CSV and XLSX formats with multiple sheets

## Data Flow

1. User opens GUI to select input files and folders
2. Main program collects paths and initializes LLM client
3. Job description is processed to extract requirements (if needed)
4. Each resume is processed:
   - Text extracted from .docx/.pdf format
   - Resume text analyzed by LLM against job requirements
   - Results stored in dictionary
5. Final analysis DataFrame created and saved as CSV
6. Results merged with HR data and exported to Excel with "Top Candidates" sheet

## Dependencies

- [`requirements.txt`](requirements.txt:1) lists external packages:
  - openpyxl: Excel file handling
  - docx2txt: .docx text extraction
  - pdfplumber: .pdf text extraction
  - pandas: Data manipulation and analysis
  - openai: LLM API client interface