
import time
import tkinter as tk
from tkinter import filedialog

# --- Global Data Storage ---

PATHS = {
    'job_description_or_requirements_file': '',
    'master_reference_file': '',
    'resume_folder': '',
    'output_folder': '',
    'existing_csv_file_or_new_file_name': '',
    'Has_Requirements_File': False,
    'Has_Existing_CSV': False,
    'isDone': False
}

# --- GUI Helper Functions ---

def select_file(entry_widget):
    """Opens a file dialog and updates the specified Entry widget with the selected file path."""
    path = filedialog.askopenfilename(title="Select File")
    if path:
        entry_widget.delete(0, tk.END)
        entry_widget.insert(0, path)

def select_folder(entry_widget):
    """Opens a folder dialog and updates the specified Entry widget with the selected folder path."""
    path = filedialog.askdirectory(title="Select Folder")
    if path:
        entry_widget.delete(0, tk.END)
        entry_widget.insert(0, path)

# --- Toggle Functions for Conditional UI Elements ---

def toggle_requirements_file_selection(file_entry, button_var, label_var, frame, row_num):
    """Manages the visibility and text for the job requirements/description file selection."""
    choice = button_var.get()

    if choice == 1:
        # Yes: User has a requirements file from previous run
        label_text = "Please select the job requirements file:"
        PATHS['Has_Requirements_File'] = True
    elif choice == 0:
        # No: User needs to select the job description file
        label_text = "Please select the job description file:"
        PATHS['Has_Requirements_File'] = False
    else:
        # Hide frame if no selection
        frame.grid_forget()
        return

    # Update label and show frame
    label_var.set(label_text)
    frame.grid(row=row_num, column=0, columnspan=3, padx=5, pady=5, sticky='w')
    file_entry.delete(0, tk.END)

def toggle_csv_file_selection(file_entry, button_var, label_var, frame, row_num):
    """Manages the visibility and text for the existing CSV file selection."""
    choice = button_var.get()

    if choice == 1:
        # Yes: User has an existing CSV file to filter
        label_text = "Please select the existing CSV file:"
        PATHS['Has_Existing_CSV'] = True
        # Show the frame
        label_var.set(label_text)
        frame.grid(row=row_num, column=0, columnspan=3, padx=5, pady=5, sticky='w')
        file_entry.delete(0, tk.END)
    elif choice == 0:
        # No: User will create a new CSV file
        label_text = "Enter a name for the new output CSV file:"
        PATHS['Has_Existing_CSV'] = False

        label_var.set(label_text)
        frame.grid(row=row_num, column=0, columnspan=3, padx=5, pady=5, sticky='w')
        file_entry.delete(0, tk.END)

    else:
        # Hide frame if no selection
        frame.grid_forget()

# --- Data Collection and GUI Exit ---

def get_paths(root, job_req_entry, csv_entry, master_ref_entry, resume_folder_entry, output_folder_entry):
    """
    Retrieves all paths from entry widgets, stores them in PATHS dictionary,
    and signals the GUI to close.
    """
    # Collect all the data
    PATHS['job_description_or_requirements_file'] = job_req_entry.get()
    PATHS['existing_csv_file_or_new_file_name'] = csv_entry.get()
    PATHS['master_reference_file'] = master_ref_entry.get()
    PATHS['resume_folder'] = resume_folder_entry.get()
    PATHS['output_folder'] = output_folder_entry.get()

    # Signal completion
    PATHS['isDone'] = True

    # Close the GUI
    root.quit()
    root.withdraw()


# --- Main Window Creation Function ---

def make_window():
    """
    Creates and runs the file selection GUI with two sets of conditional buttons:
    1. For job requirements file selection
    2. For existing CSV file selection
    """

    # Initialize the main Tkinter window
    root = tk.Tk()
    root.title("Resume Analyzer File and Folder Selection")

    # =============================================================================
    # SECTION 1: JOB REQUIREMENTS FILE SELECTION
    # =============================================================================

    # Control variables for requirements file section
    button_var_requirements = tk.IntVar(root, value=-1)  # -1=unselected, 1=Yes, 0=No
    conditional_label_requirements = tk.StringVar(root, value="")

    # Row 0: Question about existing job requirements file
    tk.Label(root, text="Do you already have a job requirements file from a previous run?").grid(
        row=0, column=0, padx=5, pady=5, sticky='w')

    # Create the conditional frame for requirements file (starts hidden)
    conditional_frame_requirements = tk.Frame(root)

    # Entry widget for job requirements/description file
    job_req_entry = tk.Entry(conditional_frame_requirements, width=50)

    # Radio buttons for requirements file question
    tk.Radiobutton(root, text="Yes", variable=button_var_requirements, value=1,
                   command=lambda: toggle_requirements_file_selection(
                       job_req_entry, button_var_requirements,
                       conditional_label_requirements, conditional_frame_requirements, 1)
                   ).grid(row=0, column=1, padx=2, pady=5, sticky='w')

    tk.Radiobutton(root, text="No", variable=button_var_requirements, value=0,
                   command=lambda: toggle_requirements_file_selection(
                       job_req_entry, button_var_requirements,
                       conditional_label_requirements, conditional_frame_requirements, 1)
                   ).grid(row=0, column=2, padx=2, pady=5, sticky='w')

    # Row 1: Conditional frame for requirements file selection (initially hidden)
    # Dynamic label for requirements file
    tk.Label(conditional_frame_requirements, textvariable=conditional_label_requirements).grid(
        row=0, column=0, padx=5, pady=5, sticky='w')

    # Entry and browse button for requirements file
    job_req_entry.grid(row=0, column=1, padx=5, pady=5)
    tk.Button(conditional_frame_requirements, text="Browse...",
              command=lambda: select_file(job_req_entry)).grid(row=0, column=2, padx=5, pady=5)

    # =============================================================================
    # SECTION 2: STANDARD FILE/FOLDER SELECTIONS
    # =============================================================================

    # Row 2: Master Reference HR File
    tk.Label(root, text="Please select the master reference HR file:").grid(
        row=2, column=0, padx=5, pady=5, sticky='w')
    master_reference_entry = tk.Entry(root, width=50)
    master_reference_entry.grid(row=2, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_file(master_reference_entry)).grid(
        row=2, column=2, padx=5, pady=5)

    # Row 3: Resume Folder
    tk.Label(root, text="Please select the folder containing the resumes:").grid(
        row=3, column=0, padx=5, pady=5, sticky='w')
    resume_folder_entry = tk.Entry(root, width=50)
    resume_folder_entry.grid(row=3, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_folder(resume_folder_entry)).grid(
        row=3, column=2, padx=5, pady=5)

    # Row 4: Output Folder
    tk.Label(root, text="Please select the folder in which to store the output:").grid(
        row=4, column=0, padx=5, pady=5, sticky='w')
    output_folder_entry = tk.Entry(root, width=50)
    output_folder_entry.grid(row=4, column=1, padx=5, pady=5)
    tk.Button(root, text="Browse...", command=lambda: select_folder(output_folder_entry)).grid(
        row=4, column=2, padx=5, pady=5)

    # =============================================================================
    # SECTION 3: EXISTING CSV FILE SELECTION
    # =============================================================================

    # Control variables for CSV file section
    button_var_csv = tk.IntVar(root, value=-1)  # -1=unselected, 1=Yes, 0=No
    conditional_label_csv = tk.StringVar(root, value="")

    # Row 5: Question about existing CSV file
    tk.Label(root, text="Do you already have a .csv file of resumes from a previous run to filter?").grid(
        row=5, column=0, padx=5, pady=5, sticky='w')

    # Create the conditional frame for CSV file (starts hidden)
    conditional_frame_csv = tk.Frame(root)

    # Entry widget for existing CSV file
    csv_entry = tk.Entry(conditional_frame_csv, width=50)

    # Radio buttons for CSV file question
    tk.Radiobutton(root, text="Yes", variable=button_var_csv, value=1,
                   command=lambda: toggle_csv_file_selection(
                       csv_entry, button_var_csv,
                       conditional_label_csv, conditional_frame_csv, 6)
                   ).grid(row=5, column=1, padx=2, pady=5, sticky='w')

    tk.Radiobutton(root, text="No", variable=button_var_csv, value=0,
                   command=lambda: toggle_csv_file_selection(
                       csv_entry, button_var_csv,
                       conditional_label_csv, conditional_frame_csv, 6)
                   ).grid(row=5, column=2, padx=2, pady=5, sticky='w')

    # Row 6: Conditional frame for CSV file selection (initially hidden)
    # Dynamic label for CSV file
    tk.Label(conditional_frame_csv, textvariable=conditional_label_csv).grid(
        row=0, column=0, padx=5, pady=5, sticky='w')

    # Entry and browse button for CSV file
    csv_entry.grid(row=0, column=1, padx=5, pady=5)
    tk.Button(conditional_frame_csv, text="Browse...",
              command=lambda: select_file(csv_entry)).grid(row=0, column=2, padx=5, pady=5)

    # =============================================================================
    # SECTION 4: FINAL CONTROLS
    # =============================================================================

    # Row 8: OK Button
    btn_ok = tk.Button(root, text="OK", width=10,
                       command=lambda: get_paths(root, job_req_entry, csv_entry,
                                               master_reference_entry, resume_folder_entry,
                                               output_folder_entry))
    btn_ok.grid(row=8, column=1, pady=10)

    # =============================================================================
    # SECTION 5: MANUAL EVENT LOOP
    # =============================================================================

    # This loop replaces root.mainloop() and keeps the window responsive
    # while waiting for the user to click OK
    while not PATHS['isDone']:
        root.update()  # Process all pending GUI events
        time.sleep(0.01)  # Brief pause to prevent excessive CPU usage

    # Clean up and return results
    root.destroy()
    return PATHS





