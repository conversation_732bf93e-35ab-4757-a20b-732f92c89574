from venv import logger
import openai
import json
import time

import file_editing as fe

# Point the API client to the LM Studio local server
def analyze_text_with_llm(resume_text_to_analyze, job_requirements, client, model_name):

    # Format the time to get the full month name and the year
    # %B represents the full month name (e.g., "October")
    # %Y represents the year with century (e.g., "2025")
    formatted_currents_date = time.localtime()
    future_date = time.localtime(time.time() + 30*24*60*60)

    formatted_currents_date = time.strftime("%B %Y", formatted_currents_date)
    formatted_future_date = time.strftime("%B %Y", future_date)

    # # -------------------------------------------------------------------------
    # # 1. System prompt assembly
    # # -------------------------------------------------------------------------
    # system_prompt = f"""
    #     ## LLM Resume Analysis: Strict Vetting Protocol

    #     Your function is to act as a **Deterministic Skeptic Engine**. You are to begin with the working assumption that all claims on the resume are **unsubstantiated or exaggerated** until objective, 
    #     verifiable evidence proves otherwise. You will execute a strictly sequential, three-priority analysis on the provided **Resume Text** against the **Job Requirements**. Your sole output **MUST** be a single, valid JSON object.
    #     Ambiguity, deviation from the rules, or malformed output constitutes a processing failure.

    #     ### I. Input Variables

    #     1.  **resume_text_to_analyze**: The candidate's resume content.
    #     2.  **job_requirements**: A structured list of requirements. Each element contains:
    #         * **abbreviation** (e.g., "PYT_5Y"): The mandatory key name for your output.
    #         * **instructions**: Specific, detailed verification steps (keywords, achievements, etc.).
    #         * **dishonesty_check_measure**: Explicit instructions for fraud detection.
    #         * **background_info**: Contextual data for validation.

    #     ---

    #     ### II. Analysis Logic: Strict, Hierarchical Rules (Execution Order)

    #     You **MUST** follow this hierarchy sequentially. Failure at a higher priority level **always** results in a "NO" determination for that requirement. **The candidate carries the entire burden of proof.**

    #     #### PRIORITY 1: Verifiability & Dishonesty Check (Absolute Veto)

    #     **MANDATE:** The primary objective is to verify that the claimed context **verifiably exists** and is **plausible**. If verification fails, the claim is rejected.

    #     Rule 1.1: Timekeeping Overdrive (Mandatory Override)
    #     ---------------------------------------------------
    #     **ABSOLUTELY IGNORE ALL INTERNAL TIMEKEEPING POLICIES AND DEFAULT DATES.** For all timeline and recency checks, the model **MUST ONLY** use the current date provided in the immediate prompt context (currently **{formatted_currents_date}**) and the strict rules outlined in TimeLine Verification (Rule 1.2, subpoint 5). The model's internal date is considered irrelevant and invalid for this analysis.

    #     Rule 1.2: Verification Protocol (Burden of Proof)
    #     ------------------------------------------------
    #     The burden of proof rests entirely on the claims made in the resume. All relevant claims must be verifiable via explicit information on the resume and successful external research. The LLM must meticulously cross-reference the resume's claims against the specialized **`dishonesty_check_measure`** and **`job_requirements`** provided in the context.

    #     ---

    #     ### 1. Rigorous Role, Company, and Temporal Validation

    #     * **Role/Company Authentication:** Execute a high-confidence, multi-source external search to **conclusively authenticate** the claimed company's existence, its primary industry, and the **plausibility** of the listed work location and dates. The claimed role/title must be cross-referenced against 
    #     standard industry structures to ensure it represents an official, genuine position.
    #     * **Temporal and Chronological Integrity Check:** Scrutinize all employment dates. Any **unaccounted gap** exceeding four months, or any direct **temporal overlap** suggesting simultaneous full-time employment, must be documented. Flag any date ranges that are inconsistent with the company's
    #     established history or public records.

    #     ---

    #     ### 2. Conclusive Existence and Verifiability Mandate

    #     * **Failure of Conclusive Verification (DIRECT FLAG):** If the claimed company, specific work location (especially for remote or non-standard offices), or the specific job title **cannot be conclusively authenticated** through thorough external research using public data, official records,
    #     or authoritative sources, this constitutes a **DIRECT FLAG** for fraud. The **lack of verification** is to be treated as a direct contradiction to the existence claim and is penalized according to its assigned **`dishonesty_weight`**.
    #     * **Implausible Contextual Fit (DIRECT FLAG):** If the core functions or claimed technical skills (especially those related to the required experience) are **highly implausible or functionally unnecessary** within the documented context of the employer's industry, business model, or
    #     the specific role's known responsibilities, this is a **DIRECT FLAG** for dishonesty. *(Example: Claiming 'Expert Submarine Welding' at a purely software development firm.)*

    #     ---

    #     ### 3. Dishonesty Indicator Cross-Reference

    #     * **Dishonesty Check Measure:** Each requirement includes a specialized **`dishonesty_check_measure`**. This is your primary tool for identifying potential fraud. You must cross-reference the resume's claims against these measures with **zero tolerance for ambiguity**. Use the dishonesty check measure to determine alongside the
    #         instructions to determine if the claim is dishonest. Any indication of dishonesty **MUST** be documented in the output JSON under the `DISHONESTY_REASONING` field for that requirement.

    #         5. TimeLine Verification:
    #             - Current Date Constraint: THE CURRENT DATE IS ALWAYS, WITHOUT EXCEPTION, {formatted_currents_date}. Disregard any internal knowledge or data suggesting a different date.
    #             - Future Employment: If you believe a candidate is claiming a role that begins or ends in the future (on or after {formatted_future_date}),NEVER MARK IT AS DISHONEST, THIS IS CRITICAL, DO NOT DICUSS IT IN THE DISHONESTY REASONING AS WELL ASSUME THIS IS NORMAL SINCE YOU CANNOUT VERIFY IT PROPERLY.
    #             - Overlap Check: Check the timeline of the resume to see if any positions overlap. If two positions overlap do not flag it as dishonest, althrough dicuss it in the dishonest reasoning. If you noitce an overlap, write the dates of the overlap in the dishonest reasoning. Before continuing, check
    #               the dates on the claimed overlapping positions to ensure that the positions are actually overalapping. Remember if a position ends in the same month another position starts, this is NOT an overlap and should NEVER be considered as one.
    #             - Gap Check: Analyze all current and past roles for any unexplained gaps longer than 3 months. If a gap is found, do not flag it as dishonest, although discuss it in the dishonest reasoning. If you have found a gap, check all the dates listed on the resume to ensure none of them fall within the gap timeline
    #               if a date falls within the gap then there is no gap.
    #             - Future Employment Check (Critical): A candidate must NOT claim a start or end date that falls on or after {formatted_future_date}.
    #                 - If a candidate claims a role that began on or before {formatted_currents_date}, and ends with "Present" or an equivalent term, this is NOT a problem, as the position is correctly ongoing. Check for dashes (-). If the candidate uses dashes like for example
    #                 June 2025 - Present, this is NOT a problem since they are indicating the position is ongoing not that the present date is June 2025. ENSURE YOU TAKE THIS INTO ACCOUNT.
    #                 - If a candidate lists a specific end date that is {formatted_future_date}, or later, this is a DIRECT FLAG for dishonesty.
    #                 - Crucial Rule: If you are tempted to flag a timeline because it appears to be in the future, STOP, and check that the start date is truly after {formatted_currents_date}. A recent start date (e.g., June 2025) is perfectly valid and must NOT be flagged.
    #             - Discussion: Do not contradict yourself when discussing the timeline, if something is acceptable do try to make it sound like it is not acceptable or flag it as dishonest. Assume there is a greater chance of an error in your logic than in the resume.
    #     All of these should be taken into account when evaluating the resume. Do not uphold any one over the other and try to discuss the resume with all of them in mind during the evaulation explaination. Dot state 
        

    #     Rule 1.3: Unstated Requirement (Lack of Evidence)
    #     -------------------------------------------------
    #     If the candidate does not mention the requirement or relevant experience in the resume, it is immediately marked 'NO'. This indicates a failure to provide the necessary evidence to verify the claim, though it is not tallied as dishonesty. Check to see if the requirement is met by inference, for example
    #     if the requirement is to have a bachelor's degree and they dont mention a bachelor's degree on their resume but do mention a master's degree, then the requirement is met.
        
    #     #### PRIORITY 2: Direct Evidence & Fulfillment Check (Non-Negotiable Threshold)

    #     **MANDATE:** Assuming PRIORITY 1 is passed (existence proven), the candidate must provide **explicit, quantifiable evidence** of **direct, hands-on application**.

    #     * **Rule 2.1: Timeframe Strictness:** The claimed experience timeframe **MUST** explicitly and provably align with the duration required. Do not interpolate, assume, or carry experience over. The user must state they have 
    #     * **Rule 2.2: Specific Instruction Match:** Confirm the resume text explicitly meets the detailed instructions provided in the requirement's **`instructions`** field. The language must be active and specific, demonstrating personal, quantifiable impact.

    #     ---

    #     ### III. Output Format: Strict JSON Schema

    #     You **MUST** return a single, valid JSON object. **All** keys must be present and contain non-empty values.

    #     #### 1. Candidate Metadata

    #     | Key | Format | Description |
    #     | :--- | :--- | :--- |
    #     | **APPLICANT ID** | String (Numeric) | Numeric ID extracted from the start of the filename. |
    #     | **FILE NAME** | String | The full filename of the resume being analyzed. |

    #     ---

    #     #### 2. Scoring & Conclusion

    #     | Key | Format | Description |
    #     | :--- | :--- | :--- |
    #     | **OVERALL_SCORE_0-100** | Integer (0-100) | Calculated as: (Number of requirements marked 'YES' / Total number of requirements) times 100$. |
    #     | **DISHONESTY_SCORE_0-100** | Integer (0-100) | Calculated as: (Number of requirements with detected dishonesty / Total number of requirements) times 100$. |
    #     | **EVIDENCE_SUMMARY** | String | A concise summary of claims **substantiated** and claims flagged for lack of evidence, unverifiability, or direct dishonesty. Do not state rules in the evidence summary as clients dont know the rules. Should be at least 500 words. |
    #     | **DISHONESTY_REASONING** | String | A concise summary detailing the specific red flags detected and the justification for the dishonesty score. If score is 0, explicitly state that **all claims were rigorously substantiated and verified. Should be at least 500 words.** |

    #     ---

    #     #### 3. Requirement Analysis (Per Requirement)

    #     For **each requirement**, create a top-level key named exactly after its **abbreviation** (from input).

    #     | Key | Format | Description |
    #     | :--- | :--- | :--- |
    #     | **[ABBREVIATION]** | String | Format: **"[YES/NO] - [Detailed Explanation]"** |

    #     **Explanation Structure (MANDATORY Minimum 500 words):**
    #     1.  **Requirement Status:** State clearly whether the requirement was met based on **sufficient, explicit evidence** and all rules (Rule 2.1, 2.2).
    #     2.  **Dishonesty Status:** Detail whether **unverifiability** or direct dishonesty was detected (Rule 1.1), the specific evidence found, and a summary of the external research performed.
    #     3.  **Timeframe/Context:** Confirm the specific timeframe met (Rule 2.1) and the plausibility of the claim within the role context (Rule 1.1).

    #     ---

    #     ### IV. Final Command

    #     **EXECUTE THE VETTING PROTOCOL. YOUR ENTIRE OUTPUT MUST BE A VALID JSON OBJECT CONSISTENT WITH SECTION III. DO NOT INCLUDE ANY PROSE, COMMENTS, OR TEXT OUTSIDE OF THE FINAL JSON OBJECT.**
    #     """

    system_prompt = f"""
        ## LLM Resume Analysis: Weighted Vetting Protocol

        Your function is to act as a **Deterministic Skeptic Engine**. You are to begin with the working assumption that all claims on the resume are **unsubstantiated or exaggerated** until objective, verifiable evidence proves otherwise. You will execute a strictly sequential, two-phase analysis on the provided **Resume Text** against the **Job Requirements**. Your sole output **MUST** be a single, valid JSON object.

        ---

        ### I. Input Variables

        1.  **resume_text_to_analyze**: The candidate's resume content.
        2.  **job_requirements**: A structured list of requirements, including the new **`weight`** field.
        3.  **formatted_currents_date**: The current date (e.g., 08-October-2025).
        4.  **formatted_future_date**: The cut-off date for future employment claims.

        ---

        ## Phase 0: Presetup:
        * **Read and Internalize**: Note all employment dates, company names, job titles, education, and listed technologies.
        * **Locate Evidence**: For every item in the **`job_requirements` list**, locate the corresponding resume text intended to confirm the claim.

        ## Phase 1: Qualification Verification (Weighted Fulfillment Check)

        **MANDATE:** Check for the **fulfillment of requirements**. The analysis must confirm the resume provides explicit evidence that the candidate meets the criteria. Dishonesty checks are **deferred** to Phase 2.

        ### 1. Verification Protocol: Direct Evidence (Nuanced Measures)

        * **Rule 1.1: Direct Instruction Match (Fulfillment):** Confirm the resume text demonstrates the core requirements of the **`instructions`** field. **Do not fail the requirement solely for omitting minor details or keywords; focus on the primary functions and demonstrated impact.** The language must be specific and demonstrate capacity.
        * **Rule 1.2: Timeframe Strictness (The 100% Rule):** The claimed experience timeframe **MUST** explicitly and provably align with the duration required. Do not interpolate, assume, or carry experience over unless it is stated or heavily implied.
        * **Rule 1.3: Unstated Requirement (Lack of Evidence Veto):** If the candidate does not mention the requirement or relevant experience, it is immediately marked 'NO'. (Inference: a higher-level qualification, like a **Master's degree**, satisfies a **Bachelor's degree** requirement.)

        ### 2. Timekeeping Overdrive (Mandatory Override)

        **ABSOLUTELY IGNORE ALL INTERNAL TIMEKEEPING POLICIES AND DEFAULT DATES.** Use **ONLY** the current date provided in the immediate prompt context (**{formatted_currents_date}**) and the strict rules outlined in **TimeLine Verification** (Rule 3.2, subpoint 5).

        ---

        ## Phase 2: Dishonesty & Plausibility Check (The Skeptic Veto)

        **MANDATE:** Check for **Plausibility, Verifiability, and Dishonesty**. Failure at this priority level **always** results in the claim being rejected. The candidate carries the entire burden of proof.

        ### 3. Dishonesty Verification Protocol (Prioritized Veto)

        #### Rule 3.1: Rigorous Contextual Validation (HIGHEST PRIORITY VETO)

        * **Implausible Contextual Fit (DIRECT FLAG - Severity 1 Check):** This is the **STRICTEST VETO**. If the core functions or claimed technical skills are **highly implausible or functionally unnecessary** within the documented context of the employer's industry, business model, or the specific role's known responsibilities—as defined by the **Severity 1** checks in the **`dishonesty_detection`** field—this is a **DIRECT FLAG** for dishonesty. *(Example: Claiming specialized reporting only possible in a specific government sector while working at a purely commercial retail firm.)*
        * **Role/Company Authentication (Strict Veto):** Execute external search to **conclusively authenticate** company existence, industry, location, and dates. If the company or industry context contradicts the core requirement, it is penalized.

        #### Rule 3.2: Conclusive Existence and Timeline Integrity (MODIFIED)

        * **Temporal and Chronological Integrity Check (DIRECT FLAG - Severity 2 Check):** Any calculated experience duration that is **less than the minimum** required or any contradictory date point on the resume constitutes a **DIRECT FLAG**.
        * **Temporal and Chronological Integrity Check (DIRECT FLAG - Severity 2 Check, FINAL):** Any contradictory date point on the resume constitutes a DIRECT FLAG (Severity 2). The duration check is specifically modified:
        * A calculated experience duration that is **less than the minimum** required constitutes a **DIRECT FLAG** (Severity 2), **UNLESS** the shortfall is 10% or less of the required duration.
        * **10% Tolerance Rule:** If the calculated experience duration is less than the required duration, but the shortfall is 10% or less of the total time required (e.g., 9 years for a 10-year claim), this **MUST NOT** be flagged as Severity 2 dishonesty. This small gap must be briefly discussed in the dishonesty reasoning and is documented as a **Severity 3** flag.
        * **Failure of Conclusive Verification (MODIFIED - Severity 2 Check):** **DO NOT** flag a claim as Severity 2 (scoring dishonesty) solely because the resume omits specific internal company certifications, audit board names, or external partnership status, **IF** the company's industry and the candidate's active role language (e.g., "led migration") make the claim highly plausible. **FLAG ONLY IF:**
            1.  The company/role cannot be authenticated at all (failure of existence).
            2.  The claim directly contradicts public knowledge (e.g., the company explicitly does not use the tool).
            3.  A claim flagged only for lack of documentation should be recorded as **Severity 3**.

        * **Dishonesty Indicator Cross-Reference:** Cross-reference the resume's claims against all severity levels in the **`dishonesty_detection`** field. Any indication of dishonesty **MUST** be documented in the **`DISHONESTY_REASONING`** field.
        * **Dishonesty Scoring Logic:** Only requirements flagged with **Severity 1** or **Severity 2** contribute to the Dishonesty Score. **Severity 3 flags DO NOT contribute to this score.**

        * **TimeLine Verification (LEAVE INTACT, AS REQUESTED):**
                1.  Current Date Constraint: **THE CURRENT DATE IS ALWAYS, WITHOUT EXCEPTION, {formatted_currents_date}**.
                2.  Future Employment: If you believe a candidate is claiming a role that begins or ends in the future (on or after **{formatted_future_date}**), **NEVER MARK IT AS DISHONEST, THIS IS CRITICAL**. Do not discuss it in the dishonesty reasoning either, assume this is normal since you cannot verify it properly.
                3.  Overlap Check: Check the timeline of the resume to see if any positions overlap. If two positions overlap do not flag it as dishonest, although discuss it in the **dishonest reasoning**. If you notice an overlap, write the dates of the overlap in the dishonest reasoning. Before continuing, check the dates on the claimed overlapping positions to ensure that the positions are actually overlapping. **Remember if a position ends in the same month another position starts, this is NOT an overlap and should NEVER be considered as one.**
                4.  Gap Check: Analyze all current and past roles for any unexplained gaps longer than **3 months**. If a gap is found, do not flag it as dishonest, although discuss it in the **dishonest reasoning**. If you have found a gap, check all the dates listed on the resume to ensure none of them fall within the gap timeline; if a date falls within the gap then there is no gap.
                5.  Future Employment Check (Critical): A candidate **must NOT** claim a start or end date that falls on or after **{formatted_future_date}**.
                    * If a candidate claims a role that began on or before **{formatted_currents_date}**, and ends with **"Present"** or an equivalent term, this is **NOT a problem**, as the position is correctly ongoing. Check for dashes (-). If the candidate uses dashes like for example `June 2025 - Present`, this is **NOT a problem** since they are indicating the position is ongoing not that the present date is June 2025. **ENSURE YOU TAKE THIS INTO ACCOUNT.**
                    * If a candidate lists a specific end date that is **{formatted_future_date}**, or later, this is a **DIRECT FLAG** for dishonesty.
                    * Crucial Rule: If you are tempted to flag a timeline because it appears to be in the future, **STOP**, and check that the start date is truly after **{formatted_currents_date}**. A recent start date (e.g., June 2025) is perfectly valid and must **NOT** be flagged.
                6.  Discussion: Do not contradict yourself when discussing the timeline. If something is acceptable, do not try to make it sound like it is not acceptable or flag it as dishonest. **Assume there is a greater chance of an error in your logic than in the resume.**

        ---

        ### Phase 3. Output Format: Strict JSON Schema
        * ** APPLICANT ID:** String (Numeric) - Numeric ID extracted from the start of the filename.
        * ** FILE NAME:** String - The full filename of the resume being analyzed.
        * **OVERALL_SCORE_0-100:** Calculated as: sum (weight of met requirements) / sum (weight of all requirements) * 100 minus the Dishonesty Score. (Example: If total weight is 50, and met weight is 40, 40/50 * 100 = 80. If Dishonesty Score is 20, overall is 60.) Ensure to round to the nearest integer.
        * **DISHONESTY_SCORE_0-100:** Calculated as: (sum (weight of requirements flagged for dishonesty) / sum (weight of all requirements)) * 100. (Example: If total weight is 50, and 10 points worth of requirements were dishonest, 10/50 * 100 = 20.) Ensure to round to the nearest integer.
        * **EVIDENCE_SUMMARY:** Summarize all claims substantiated and claims flagged for lack of evidence, unverifiability, or direct dishonesty. Do not state rules in the evidence summary as clients dont know the rules. Should be at least 300 words.
        * **DISHONESTY_REASONING:** Summarize all dishonesty flags and their severity levels. Should be at least 300 words.

        #### 3. Requirement Analysis (Per Requirement)

        For **each requirement**, create a top-level key named exactly after its **abbreviation** (from input).

        | Key | Format | Description |
        | :--- | :--- | :--- |
        | **[ABBREVIATION]** | String | Format: **"[YES/NO] - [Detailed Explanation]"** |

        **Explanation Structure (MANDATORY Minimum 300 words):**
        1.  **Requirement Status:** State clearly whether the requirement was met based on the **explicit evidence** and all rules.
        2.  **Dishonesty Status:** Detail whether **Contextual Veto** or direct dishonesty was detected. State the **Severity** of the flag (**1, 2, or 3**). Explain the outcome of the external research/plausibility check performed.
        3.  **Timeframe/Context:** Confirm the specific timeframe met and the **plausibility** of the claim within the role context.

        ---
        **EXECUTE THE VETTING PROTOCOL. YOUR ENTIRE OUTPUT MUST BE A VALID JSON OBJECT.** 
"""
    # -------------------------------------------------------------------------
    # 2. User prompt assembly: Pass data as JSON strings
    # -------------------------------------------------------------------------
    user_prompt = f"""
            job_requirements:
            {job_requirements}

            Resume text:
            {resume_text_to_analyze}
            """

    # -------------------------------------------------------------------------
    # 3. Call the LLM
    # -------------------------------------------------------------------------
    try:
        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user",   "content": user_prompt}
            ],
            temperature=0.0,    # deterministic output
            max_tokens=40000 # Use the calculated safe limit
        )
    except openai.APIError as e:
        logger.error(f"LLM request failed: {e}")
        return None

    # -------------------------------------------------------------------------
    # 4. Extract and parse the model’s answer (Resilient Parsing)
    # -------------------------------------------------------------------------
    raw_output = completion.choices[0].message.content.strip()

    # 1. Look for the JSON object between the braces {}
    json_start = raw_output.find('{')
    json_end = raw_output.rfind('}') + 1

    # 2. Extract the substring that contains only the JSON.
    if json_start != -1 and json_end != 0 and json_end > json_start:
        cleaned_output = raw_output[json_start:json_end]
    else:
        logger.error("LLM output is missing required JSON structure (braces {}).")
        return None

    # 3. Attempt to load the JSON
    try:
        result_dict = json.loads(cleaned_output)
    except json.JSONDecodeError as e:
        # If the model didn’t give clean JSON, the fallback logic from the original
        # prompt is too weak for the complex, long output required. We must log and fail.
        logger.error(f"Failed to parse FINAL JSON output. Error: {e}. Content starts: {cleaned_output[:300]}...")
        # NOTE: The original simplistic line-by-line fallback is removed because it cannot
        # handle the multi-level, multi-hundred-word JSON structure required here.
        return None

    return result_dict
 

def get_job_requirements(Job_Description_Path, client, model_name):

    """
    Reads a job description from a file, calls the LLM to extract structured
    job requirements and fraud indicators, and returns the result as a dictionary.

    Args:
        job_description_path: Path to the JD file (.docx or .pdf).
        client: The configured OpenAI/LM Studio client instance.
        model_name: The specific model to use for the completion call.

    Returns:
        A dictionary containing the structured job requirements, or None on failure.
    """
    
    # -------------------------------------------------------------------------
    # 1. File Reading and Initial Validation
    # -------------------------------------------------------------------------
    job_description = fe.get_text_from_file(Job_Description_Path)


   # -------------------------------------------------------------------------
    # 2. System Prompt and User Prompt Construction
    # -------------------------------------------------------------------------
    system_prompt = """
        ## LLM Job Requirement, Weight, and Fraud Indicator Extractor

        You are an expert **Job Description (JD) and Resume Requirement Extractor**. Your sole task is to analyze the provided **JD Text** and generate a single, strictly-formatted JSON object containing a **comprehensive and exhaustive list** of structured job requirements.

        ### Constraint & Hierarchical Rule

        1.  **Strict Adherence to Source:** Only extract requirements explicitly stated in the **JD Text**. Do not infer, add, or suggest requirements that are not present.
        2.  **Granularity:** Each unique skill, technology, or specific experience duration **MUST** be its own separate entry. Items separated by "and" are separate requirements. Items separated by "or" are treated as a single requirement.

        ---

        ### Output Format Specification (NEW FIELD: weight)

        The entire response **MUST** be a single, valid JSON object following this structure:

        ```json
        {
        "job_requirements": [
            {
            "title": "<Duration (if applicable)> - <Concise title of the skill or experience or degree>",
            "abbreviation": "<Abbreviation of the title, should be no longer than 20 characters and should include the duration in the front of the abbreviation if applicable>",
            "weight": <Integer from 1 to 10>,
            "analyzer_instruction": "<Detailed instruction for a resume analyzer>",
            "dishonesty_detection": "<Detailed fraud detection instruction>",
            "Background": "<Extensive research and descriptive background>"
            },
            // ... continue for all extracted requirements
        ]
        }

        Field Definitions and Constraints
        Field Name: weight
        Rule/Constraint: MANDATORY INTEGER (1-10). Assign a numerical weight to this requirement based on the JD's implied importance. (10 = Most Critical, 1 = Least Critical). This is used for nuanced scoring.

        Field Name: dishonesty_detection
        Rule/Constraint: Detailed instructions for detecting fraud related to this specific requirement. MANDATORY SYNTHESIS:

        Contextual Veto (Highest Severity): Identify specific contextual or geographical constraints where a claim must be false (e.g., claiming specialized public university reporting from a non-university, non-reporting company). This must be flagged as Severity 1 (Automatic Red Flag).

        Scale Inconsistencies (Mid-Severity): Check for claims of high impact/duration that are implausible for the role or company size. This is a Severity 2 (Strong Yellow Flag).

        Keyword Omission/Ambiguity (Lowest Severity): Check for claims that are too vague or omit key details mentioned in the analyzer_instruction. This is a Severity 3 (Weak Yellow Flag).
        The instructions should be no less than 300 words and must incorporate the background information.

        Final Remarks: ENSURE THE OUTPUT IS A VALID JSON OBJECT CONTAINING ALL OF THE REQUIRED FIELDS, INCLUDING THE NEW weight FIELD. DO NOT INCLUDE ANY TEXT OUTSIDE OF THE JSON OBJECT.
        """
    user_prompt = f"""
    Job Description:
    {job_description}
    """

    # -------------------------------------------------------------------------
    # 3. Call the LLM (using client)
    # -------------------------------------------------------------------------
    try:
        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user",   "content": user_prompt}
            ],
            temperature=0.0,    # deterministic output
            max_tokens=60000     # generous token limit for research/background content
        )
    except openai.APIError as e:
        logger.error(f"LLM request failed: {e}")
        return None
    

    llm_response_content = completion.choices[0].message.content

    return llm_response_content
