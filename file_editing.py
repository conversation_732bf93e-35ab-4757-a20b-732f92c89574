import docx2txt
import pdfplumber
import os
import pandas as pd

def merge_data_to_excel(
    file_path: str,
    sheet_name: str,
    resume_analysis_results: pd.DataFrame,
    output_workbook_path: str
) -> None:
    """
    Merges raw HR data (CSV or XLSX) with LLM resume analysis results, cleans the data,
    calculates candidate scores, and writes the results and a 'Top Candidates' summary
    to a new Excel file.

    Args:
        file_path: Path to the raw HR data file (must be .xlsx or .csv).
        sheet_name: The specific sheet name to read if file_path is an Excel file.
        resume_analysis_results: DataFrame containing the LLM's analysis (must have 'APPLICANT ID').
        output_workbook_path: The full path to save the final output Excel file.

    Returns:
        None: Writes data to disk.
    """
    # ----------------------------------------------------------------------
    # 1. Input Validation and Data Loading
    # ----------------------------------------------------------------------
    
    # Ensure the critical score column is an integer, making scores comparable
    # The 'APPLICANT ID' column is critical for the merge, ensure it is an integer type
    try:
        resume_analysis_results['OVERALL_SCORE_0-100'] = resume_analysis_results['OVERALL_SCORE_0-100'].astype(int)
        resume_analysis_results['APPLICANT ID'] = resume_analysis_results['APPLICANT ID'].astype(int)
    except KeyError as e:
        print(f"Error: Missing critical column in analysis results: {e}. Cannot proceed.")
        return
    except ValueError:
        # This occurs if conversion to int fails (e.g., non-numeric IDs or scores)
        print("Error: 'APPLICANT ID' or 'OVERALL_SCORE_0-100' columns contain non-numeric data.")
        return

    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == '.xlsx':
        # Read Excel, resetting index ensures clean merge keys
        hr_data = pd.read_excel(file_path, sheet_name=sheet_name).reset_index(drop=True)
    elif file_extension == '.csv':
        hr_data = pd.read_csv(file_path)
    else:
        print(f"Error: Unsupported file extension: {file_extension}. Must be .xlsx or .csv.")
        return
    
    # Ensure HR data has the correct ID type for merging
    try:
        hr_data['APPLICANT ID'] = hr_data['APPLICANT ID'].astype(int)
    except KeyError:
        print("Error: The HR data file is missing the 'APPLICANT ID' column.")
        return
    except ValueError:
        print("Error: 'APPLICANT ID' column in HR data contains non-numeric values.")
        return


    # ----------------------------------------------------------------------
    # 2. Data Merging and Column Renaming
    # ----------------------------------------------------------------------

    # Merge dataframes on the common integer ID, keeping only candidates with analysis results
    candidate_data = pd.merge(hr_data, resume_analysis_results, on='APPLICANT ID', how='inner')

    # Standardize names for key HR fields
    column_mapping = {
        'APPLICANT FIRST NAME': 'FIRST NAME',
        'APPLICANT LAST NAME': 'LAST NAME',
        'EMAIL ADDRESS': 'EMAIL',
        'Are you currently eligible to work in the United States of America?': 'WORKS IN US',
        'Will you now or in the future require University of Michigan visa sponsorship for continued employment in the USA?': 'NEEDS VISA/SPONSORSHIP',
    }
    candidate_data = candidate_data.rename(columns=column_mapping)

    
    # ----------------------------------------------------------------------
    # 3. Final Column Selection and Preparation
    # ----------------------------------------------------------------------

    # Filter out columns created by pandas that start with 'Unnamed'
    resume_analysis_columns = [
        col for col in resume_analysis_results.columns if not col.startswith('Unnamed')
    ]
    
    # Define final display order for output integrity
    columns_to_keep = [
        'APPLICANT ID', # Include ID at the front
        'FIRST NAME',
        'LAST NAME',
        'EMAIL',
        'WORKS IN US',
        'NEEDS VISA/SPONSORSHIP'
    ] + resume_analysis_columns
    
    # Select only the relevant columns and reset index
    candidate_data = candidate_data[columns_to_keep].reset_index(drop=True)

    # Identify and rank the top candidates based on the Overall Score
    top_candidates_df = candidate_data.sort_values(
        by='OVERALL_SCORE_0-100', 
        ascending=False
    ).head(10)

    
    # ----------------------------------------------------------------------
    # 4. Write to Excel
    # ----------------------------------------------------------------------

    # Use pd.ExcelWriter for atomic writing to multiple sheets
    try:
        with pd.ExcelWriter(output_workbook_path) as writer:
            candidate_data.to_excel(writer, sheet_name='Resume Analysis Results', index=False)
            top_candidates_df.to_excel(writer, sheet_name='Top Candidates', index=False)
        print(f"Successfully wrote analysis results to: {output_workbook_path}")
    except Exception as e:
        print(f"Error writing to Excel file: {e}")


def get_text_from_file(file_path):
    """
    Extracts text content from .docx and .pdf files.

    Args:
        file_path: The full path to the file (e.g., a resume file).

    Returns:
        The extracted text as a single string, or None if the file type
        is unsupported or if a critical reading error occurs.
    """
    # Get the file extension
    file_extension = os.path.splitext(file_path)[1].lower()
    
    # Handle .docx files
    if file_extension == '.docx':
        text = docx2txt.process(file_path)
        return text
    
    # Handle .pdf files
    elif file_extension == '.pdf':
        print("Reading PDF file")
        try:
            with pdfplumber.open(file_path) as pdf:
                full_text = []
                for page in pdf.pages:
                    full_text.append(page.extract_text())
                return '\n'.join(full_text)
        except Exception as e:
            print(f"Error reading PDF file {file_path}: {e}")
            return None
    
    # Return None for unsupported file types
    else:
        return None
