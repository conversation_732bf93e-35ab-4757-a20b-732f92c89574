import tkinter as tk
from tkinter import filedialog
import sys

# Global state to hold the selected path and choice (simplified for this standalone example)
GUI_STATE = {
    'choice': None,
    'file_path': None
}

# Global references for the dynamic widgets (needed for the helper functions)
CONDITIONAL_FRAME = None
FILE_ENTRY_WIDGET = None
BUTTON_VAR = None
LABEL_VAR = None

# --- GUI Helper Functions ---

def select_file(entry_widget):
    """Opens a file dialog and updates the specified Entry widget with the selected file path."""
    path = filedialog.askopenfilename(title="Select File")
    if path:
        entry_widget.delete(0, tk.END)
        entry_widget.insert(0, path)

def toggle_file_selection():
    """Manages the visibility and text of the conditional file selection frame."""
    global CONDITIONAL_FRAME, LABEL_VAR, BUTTON_VAR

    choice = BUTTON_VAR.get()
    
    # Determine the label text based on the radio button value (1 for Yes, 0 for No)
    if choice == 1:
        # Yes: User has the requirements file
        label_text = "Please select the job_requirements file:"
    elif choice == 0:
        # No: User needs to select the job description
        label_text = "Please select the job description file:"
    else:
        # Should not be reached if initial value is set, but good practice
        CONDITIONAL_FRAME.grid_forget()
        return

    # 1. Update the dynamic label's text
    LABEL_VAR.set(label_text)
    
    # 2. Make the conditional frame visible using grid()
    CONDITIONAL_FRAME.grid(row=1, column=0, columnspan=3, padx=5, pady=5, sticky='w')
    
    # 3. Clear any previous file path in the Entry widget for clarity
    FILE_ENTRY_WIDGET.delete(0, tk.END)

def get_final_selection(root):
    """Retrieves the final data, stores it, and closes the window."""
    global GUI_STATE, BUTTON_VAR, FILE_ENTRY_WIDGET

    # 1. Secure the data
    GUI_STATE['choice'] = "Yes" if BUTTON_VAR.get() == 1 else "No"
    GUI_STATE['file_path'] = FILE_ENTRY_WIDGET.get()
    
    # 2. Close the window cleanly
    root.quit()
    root.destroy()

# --- Main Window Creation ---

def make_window():
    global CONDITIONAL_FRAME, FILE_ENTRY_WIDGET, BUTTON_VAR, LABEL_VAR
    
    root = tk.Tk()
    root.title("Dynamic File Selector")
    
    # Initialize the Tkinter control variables
    BUTTON_VAR = tk.IntVar(root, value=-1) # Holds 1 (Yes) or 0 (No)
    LABEL_VAR = tk.StringVar(root, value="")

    # --- Row 0: Question and Radio Buttons ---
    
    tk.Label(root, text="Do you already have a job requirements file from a previous run?").grid(row=0, column=0, padx=5, pady=5, sticky='w')

    # Radio Button for YES
    tk.Radiobutton(root,
                   text="Yes",
                   variable=BUTTON_VAR,
                   value=1,
                   command=toggle_file_selection).grid(row=0, column=1, padx=2, pady=5, sticky='w')

    # Radio Button for NO
    tk.Radiobutton(root,
                   text="No",
                   variable=BUTTON_VAR,
                   value=0,
                   command=toggle_file_selection).grid(row=0, column=2, padx=2, pady=5, sticky='w')

    # --- Row 1: The Conditional File Selection Frame (Starts Hidden) ---
    
    CONDITIONAL_FRAME = tk.Frame(root)
    # Note: We do NOT call grid() yet, the frame starts hidden.

    # Dynamic Label (uses LABEL_VAR)
    dynamic_label = tk.Label(CONDITIONAL_FRAME, textvariable=LABEL_VAR)
    dynamic_label.grid(row=0, column=0, padx=5, pady=5, sticky='w')

    # Entry widget for the file path
    FILE_ENTRY_WIDGET = tk.Entry(CONDITIONAL_FRAME, width=50)
    FILE_ENTRY_WIDGET.grid(row=0, column=1, padx=5, pady=5)

    # Browse Button
    btn_browse = tk.Button(CONDITIONAL_FRAME,
                           text="Browse...",
                           command=lambda: select_file(FILE_ENTRY_WIDGET))
    btn_browse.grid(row=0, column=2, padx=5, pady=5)
    
    # --- Row 2: OK Button ---
    
    btn_ok = tk.Button(root, text="OK", command=lambda: get_final_selection(root), width=10)
    btn_ok.grid(row=2, column=1, pady=10)
    
    # Run the standard main loop for this example
    root.mainloop()

# --- Execution Block ---

if __name__ == "__main__":
    make_window()
    
    # Output the final result after the window closes
    print("\n--- Final Selection ---")
    print(f"User Choice: {GUI_STATE['choice']}")
    print(f"Selected File Path: {GUI_STATE['file_path']}")