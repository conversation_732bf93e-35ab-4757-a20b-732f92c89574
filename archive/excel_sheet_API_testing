from openpyxl import load_workbook
import pandas as pd

workbook = load_workbook(filename="IA_MalaiwatPermResEnvScan.xlsx")
sheet = workbook['Min Requrements']

resume_analysis_results = pd.read_csv("resume_analysis_results.csv")

header = [cell.value for cell in sheet[1]]

results_dict = resume_analysis_results.set_index('APPLICANT ID').to_dict('index')


for row_number, row in enumerate(sheet.iter_rows(min_row=2), start=2):
    if row[0] is None or row[0].value is None:
        break  # Stop processing if the first cell in the row is empty
    excel_applicant_id = int(row[0].value)

    columns_to_update = [
        'RESUME NAME',
        '2+Yr HEIDI',
        '2+Yr IPEDS',
        'MBA/MIS',
        '2+Yr Bus Objects Dev',
        '2+Yr Tableau Dev',
        '2+Yr SQL',
        '2+Yr Public HE Summary Reports',
        'Meets All on Resume',
        'Evidence Summary'
    ]

    for column_name in columns_to_update:
        if excel_applicant_id in results_dict:
            matching_data = results_dict[excel_applicant_id]
            
            if column_name in header:
                try:
                    # Get the value from your dictionary
                    value_to_update = matching_data[column_name]
                    
                    # Find the corresponding cell and update its value
                    row[header.index(column_name)].value = value_to_update
                except KeyError:
                    # Handle cases where the key might be missing from the LLM's output
                    print(f"Warning: Key '{column_name}' not found in LLM output for this resume.")

workbook.save(filename="IA_MalaiwatPermResEnvScan_updated.xlsx")

def update_excel_with_analysis(WorkbookPath, SheetName, AnalysisCSVPath, OutputWorkbookPath):
    workbook = load_workbook(filename=WorkbookPath)
    sheet = workbook[SheetName]

    resume_analysis_results = pd.read_csv(AnalysisCSVPath)

    header = [cell.value for cell in sheet[1]]

    results_dict = resume_analysis_results.set_index('APPLICANT ID').to_dict('index')


    for row_number, row in enumerate(sheet.iter_rows(min_row=2), start=2):
        if row[0] is None or row[0].value is None:
            break  # Stop processing if the first cell in the row is empty
        excel_applicant_id = int(row[0].value)

        columns_to_update = [
            'RESUME NAME',
            '2+Yr HEIDI',
            '2+Yr IPEDS',
            'MBA/MIS',
            '2+Yr Bus Objects Dev',
            '2+Yr Tableau Dev',
            '2+Yr SQL',
            '2+Yr Public HE Summary Reports',
            'Meets All on Resume',
            'Evidence Summary'
        ]

        for column_name in columns_to_update:
            if excel_applicant_id in results_dict:
                matching_data = results_dict[excel_applicant_id]
                
                if column_name in header:
                    try:
                        # Get the value from your dictionary
                        value_to_update = matching_data[column_name]
                        
                        # Find the corresponding cell and update its value
                        row[header.index(column_name)].value = value_to_update
                    except KeyError:
                        # Handle cases where the key might be missing from the LLM's output
                        print(f"Warning: Key '{column_name}' not found in LLM output for this resume.")

    workbook.save(filename=OutputWorkbookPath)