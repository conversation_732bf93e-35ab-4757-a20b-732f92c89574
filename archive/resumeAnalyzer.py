import os
from openpyxl import load_workbook
from venv import logger
import docx
import pdfplumber
import openai
import json
import pandas as pd
import time


# Point the API client to the LM Studio local server
client = openai.OpenAI(
    # My LM Studio base_url 
    # base_url="http://127.0.0.1:1234/v1",

    #Dan's LM Studio base_url
    base_url= "http://141.216.131.158:1234/v1",
    api_key="lm-studio" # The API key is not required for LM Studio, but this value is a common placeholder.
)


def analyze_text_with_llm(text_to_analyze):
    """
    Sends a résumé (or any free‑form text) to the local LLM and returns a dictionary
    indicating whether each of the seven required qualifications is satisfied.

    Returns:
        dict | None – keys are exactly the column headings you need for your spreadsheet.
    """

    # -------------------------------------------------------------------------
    # System prompt – set the role & expectations
    # -------------------------------------------------------------------------

    system_prompt = (
        "You are a résumé reviewer with expertise in higher‑education compliance, "
        "business intelligence tools and graduate education. Your job is to read the "
        "provided text, locate any mentions of relevant degrees, years of experience, "
        "and specific technologies, then decide whether each requirement listed below "
        "is satisfied. Return **only** a JSON object with the exact keys shown in the "
        "user prompt. Use \"Yes\" if the candidate meets the criterion and \"No\" otherwise."
    )

    # -------------------------------------------------------------------------
    # User prompt – list criteria, give clear examples, then paste the résumé
    # -------------------------------------------------------------------------
    user_prompt = f"""\
        ### Requirements (use these keys exactly):
        - RESUME NAME           : The filename of the resume being analyzed.
        - APPLICANT ID          : The applicant ID that is found inside of the filename. It should be a number found in thefirst part of the filename.
        - MBA/MIS               : Has an MBA, MIS or a related graduate degree.
        - 2+Yr HEIDI            : Two or more years doing HEIDI reporting for Michigan higher‑education compliance.
        - 2+Yr IPEDS            : Two or more years of IPEDS federal compliance reporting.
        - 2+Yr Bus Objects Dev  : Two or more years of Business Objects development.
        - 2+Yr Tableu Dev      : Two or more years of Tableau development.
        - 2+Yr SQL              : Two or more years of SQL experience.
        - 2+Yr Public HE Summary Reports : Two or more years of public higher‑education summary reports.
        - Meets All on Resume  : Does the candidate meet all of the above requirements?
        - Requirements Score              : A numeric score from 0 to 100 representing how well the candidate meets the requirements. A 0 means none of the requirements are met and the person is not a good fit for this position at all. A 100 means all of the requirements are met and the person is an excellent fit for this position. Make sure it is in the format score/100.
        - Fraud Score : Parse the resume and extract all factual claims that can be cross‑checked:
            Employment dates, locations, and company names.
            Project titles, reports, publications, or certifications (e.g., “HEIDI State Report”, “Michigan Public Higher Education Institutions”).
            Educational institutions and degrees.
            Any claim of involvement with government agencies, public datasets, or industry‑specific standards.
            Assess plausibility for each claim using the following heuristics (apply them in order; any failure raises the fraud risk):

            a. Geographic Consistency – Does the claimed role’s location align with the organization’s known headquarters/operations? Research the organization's website or use public records to verify their location matches the claimed location. For example HEIDI reporting is done for Michigan higher education institutions and therefore the location should be in Michigan.

            Example: An IT professional working for “XYZ Corp” listed as located in India, but the company is publicly known to operate only in the United States.

            b. Domain‑Specific Knowledge – Is the person’s job function compatible with the claimed activity?

            Example: An IT support engineer claiming authorship of a statewide education report (HEIDI) that is typically produced by academic research teams or government analysts or someone outside of Michigan claiming to work on HEIDI reporting.

            c. Public Record Verification – Does the referenced project/report exist in publicly searchable databases, official websites, or scholarly repositories?

            Example: An Analyst working for "XYZ Corp" claims to have written a research paper on "Compliance Reporting in Higher Education"

            d. Temporal Consistency – Are dates realistic (e.g., a 2‑year tenure overlapping with a full‑time degree program without explanation)?

            e. Redundancy & Overstatement – Does the resume repeatedly claim the same high‑impact achievement across unrelated roles?

            f. Tools & Technologies - Does the candidate clain experience in a tool providing an example that is not possible or does not make sense?

            Quantify the risk:

            Start with a base score of 0.
            Add +20 for each geographic inconsistency.
            Add +25 for domain mismatch (e.g., IT role claiming academic research output).
            Add +30 if the referenced project/report cannot be found in public sources.
            Add +15 for temporal conflicts.
            Add +10 for repeated overstatement without supporting evidence.
            Cap the final score at 100.
            Generate Output in this exact JSON format:

        - Fraud Score Reasoning  : Provide a brief summary of the reasons for the fraud score including any red flags found in the resume or why you believe the candidate is not being truthful with their qualifications.
        - Candidate Score: A score that takes into account both the requirements score and the fraud score.  The candidate score is the requirements score minus the fraud score.  The candidate score should be a number between 0 and 100.  The candidate score is the score that should be used to rank the candidates.
        - Evidence Summary  : Provide a brief summary of the evidence found in the resume that supports the qualifications or lack of qualifications. Take into account both the score and fraud score when writing the evidence summary. If the candidate claims they have a qualification but the resume does not provide enough evidence to support the claim then the evidence summary should reflect that.

        ### How to evaluate:
        1.  Thouroughly review the text provided for each candidate's resume.  Before you answer, think deeply about the text provided.

        2.  Ask yourself what are some examples of graduate level technical degreees.

        3.  Ask yourself what is HEIDI, IPEDS, Business Objects, Compliance reporting, Business Objects, Tableau, SQL, and Higher education summary reports.

        4.  Ask yourself what are some examples of Higher education summary reports.

        5. Ask yourself how accurate is the resume text in reguarding the candidate's qualifications. Does it make sense for the candidate to have the qualifications they claim to have?  If not then answer with No. 

        6.  Now that you have thought about the requirment review the resume text again.  Interpret what is provided to identify if anything you have considered is mentioned in the resume text provided.

        7. **Degrees** – Look for any phrase that contains “MBA”, “Master of Business Administration”, or references some type of masters or doctoral level study, 
        “MIS”, “Master of Information Systems”, or other graduate level degrees that are technical in nature can be considered.  
        *Example*: “John Doe, MBA, University of Michigan (2020)” → satisfies **MBA/MIS**.

        8. **Years of experience** – Identify numeric expressions tied to a specific activity/tool.
        - Direct phrasing: “3 years of HEIDI reporting” → 3 years for HEIDI.  
        - Date ranges: “2018‑2021 (HEIDI reporting)” → infer 4 years (inclusive).  
        - Multiple mentions can be summed if they refer to the same activity/tool.

        9. **Technology / Activity** – Detect keywords:
        - **HEIDI**, **IPEDS**, **Business Objects**, **Tableau**, **SQL**, **Public Higher‑Education Summary Reports** (or synonyms like “public HE reports”, “state education summary”).

        10. **Decision rule** – If the total counted years for a given activity ≥ 2, answer **Yes**; otherwise **No**.  Keep in mind that experince from multiple jobs can be taliied together.

        Make sure to answer all of the above questions. DO NOT RETURN ANYTHING OTHER THAN A JSON OBJECT WITH THE EXACT KEYS SHOWN ABOVE. Ensure the JSON object is surrounded by triple backticks. If it is not surrounded by triple backticks you will need to try again.  Ensure you check your work before you pass back the JSON object. If it is not surrounded by triple backticks you will need update your response to ensure it is.
        Resume text:
        {text_to_analyze}
        """


    # -------------------------------------------------------------------------
    # Call the LLM (using your LM Studio client)
    # -------------------------------------------------------------------------
    try:
        completion = client.chat.completions.create(
            model="openai/gpt-oss-20b",          # replace if you use a different model
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user",   "content": user_prompt}
            ],
            temperature=0.0,    # deterministic output
            max_tokens=10000     # plenty for the tiny JSON response
        )
    except openai.APIError as e:
        logger.error(f"LLM request failed: {e}")
        return None

    # -------------------------------------------------------------------------
    # Extract and parse the model’s answer
    # -------------------------------------------------------------------------
    raw_output = completion.choices[0].message.content.strip()
    # 1. Clean the output by removing the markdown fence and any surrounding text.
    # Find the start and end of the JSON object
    json_start = raw_output.find('{')
    json_end = raw_output.rfind('}') + 1  # Add 1 to include the closing brace
    

    # 2. Extract the substring that contains only the JSON.
    cleaned_output = raw_output[json_start:json_end]

    # print(cleaned_output)
    # output_df = pd.json_normalize(json.loads(cleaned_output))
    # print(output_df)
    # The LLM should have returned a JSON object, but we defensively try to parse it.
    try:

        result_dict = json.loads(cleaned_output)

    except json.JSONDecodeError:
        # print(text_to_analyze)
        # If the model didn’t give clean JSON, attempt a simple fallback:
        logger.warning("Model output not valid JSON. Trying line‑by‑line extraction.")
        result_dict = {}
        for line in raw_output.splitlines():
            # Expected format: "key : Yes" or "key : No"
            if ":" in line:
                key, val = line.split(":", 1)
                key = key.strip()
                val = val.strip().capitalize()   # normalize to Yes/No
                if key and val in {"Yes", "No"}:
                    result_dict[key] = val

        # If we still didn’t get any keys, give up.
        if not result_dict:
            logger.error("Unable to parse LLM output into the required structure.")
            return None

    # -------------------------------------------------------------------------
    # Ensure all expected columns are present (fill missing with "No")
    # -------------------------------------------------------------------------
    expected_keys = [
        "MBA/MIS",
        "2+Yr HEIDI",
        "2+Yr IPEDS",
        "2+Yr Bus Objects Dev",
        "2+Yr Tableu Dev",
        "2+Yr SQL",
        "2+Yr Public HE Summary Reports",
        "Meets All on Resume",
        "Requirements Score",
        "Fraud Score",
        "Fraud Score Reasoning",
        "Candidate Score",
        "Evidence Summary"
    ]

    return result_dict
 

def get_text_from_file(file_path):
    # Get the file extension
    file_extension = os.path.splitext(file_path)[1].lower()
    
    # Handle .docx files
    if file_extension == '.docx':
        doc = docx.Document(file_path)
        full_text = [para.text for para in doc.paragraphs]
        return '\n'.join(full_text)
    
    # Handle .pdf files
    elif file_extension == '.pdf':
        try:
            with pdfplumber.open(file_path) as pdf:
                full_text = []
                for page in pdf.pages:
                    full_text.append(page.extract_text())
                return '\n'.join(full_text)
        except Exception as e:
            print(f"Error reading PDF file {file_path}: {e}")
            return None
    
    # Return None for unsupported file types
    else:
        return None



def update_excel_with_analysis(workbook_path, SheetName, resume_analysis_results, OutputWorkbookPath):
    workbook = load_workbook(filename=workbook_path)
    sheet = workbook[SheetName]

    header = [cell.value for cell in sheet[1]]

    resume_analysis_results['APPLICANT ID'] = resume_analysis_results['APPLICANT ID'].astype(int)
    results_dict = resume_analysis_results.set_index('APPLICANT ID').to_dict('index')

    for row_number, row in enumerate(sheet.iter_rows(min_row=2), start=2):
        if row[0] is None or row[0].value is None:
            break  # Stop processing if the first cell in the row is empty
        excel_applicant_id = int(row[0].value)

        columns_to_update = [
            'RESUME NAME',
            '2+Yr HEIDI',
            '2+Yr IPEDS',
            'MBA/MIS',
            '2+Yr Bus Objects Dev',
            '2+Yr Tableu Dev',
            '2+Yr SQL',
            '2+Yr Public HE Summary Reports',
            'Meets All on Resume',
            'Requirements Score',
            'Fraud Score',
            'Fraud Score Reasoning',
            'Candidate Score',
            'Evidence Summary'
        ]

        for column_name in columns_to_update:
            if excel_applicant_id in results_dict:
                matching_data = results_dict[excel_applicant_id]
                
                if column_name in header:
                    try:
                        # Get the value from your dictionary
                        value_to_update = matching_data[column_name]
                        
                        # Find the corresponding cell and update its value
                        row[header.index(column_name)].value = value_to_update
                    except KeyError:
                        # Handle cases where the key might be missing from the LLM's output
                        print(f"Warning: Key '{column_name}' not found in LLM output for this resume.")
                
                else:
                    print(f"Warning: Column '{column_name}' not found in the Excel sheet.")

    workbook.save(filename=OutputWorkbookPath)

if __name__ == "__main__":

    start_time = time.time()
    resume_dic = {}
    # Example usage in your main loop
    folder_path = '/Users/<USER>/Library/CloudStorage/<EMAIL>/.shortcut-targets-by-id/1OO486N8MAKWvmxUcM5wedRlWy-nm32eM/Moe_Green_Card/EnvScanResumesRaw'
    # folder_path = '/Users/<USER>/Library/CloudStorage/<EMAIL>/.shortcut-targets-by-id/1OO486N8MAKWvmxUcM5wedRlWy-nm32eM/Moe_Green_Card/Resume Rename Scripting/Resumes'
    # folder_path = 'Resumes'
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        resume_text = get_text_from_file(file_path)
        resume_text = f'{filename} {resume_text}'  # Prepend filename to the text for context
        if resume_text:
            result = analyze_text_with_llm(resume_text)
            if isinstance(result, dict):
                resume_dic[filename] = result
                print('\n\n')
                print(f"Result for {filename}: {result}")
                print('\n\n')
            else:
                print(f"Unexpected result format for {filename}: {result}")
        else:
            print(f"Unsupported file type or error reading file: {filename}")
    end_time = time.time()

    time_taken = (end_time - start_time) / 60
    print(f"Total time taken: {time_taken} minutes")

    analysis_df = pd.DataFrame.from_dict(resume_dic, orient='index')

    analysis_df.to_csv('Output_Files/resume_analysis_results.csv')
    
    update_excel_with_analysis('Input_Files/IA_MalaiwatPermResEnvScan.xlsx', 'Min Requrements', analysis_df, 'Output_Files/IA_MalaiwatPermResEnvScan_Updated.xlsx')