import os
import docx
import pdfplumber
import openai


# # Point the API client to the LM Studio local server
# client = openai.OpenAI(
#     base_url="http://127.0.0.1:1234/v1",
#     api_key="lm-studio" # The API key is not required for LM Studio, but this value is a common placeholder.
# )


# def analyze_text_with_llm(text_to_analyze):
#     """
#     Sends text to the local LLM for analysis and returns the output.
#     """
#     try:
#         # Create a chat completion request
#         completion = client.chat.completions.create(
#             model="qgoogle/gemma-3-4b",  # Replace with the identifier of the model you loaded in LM Studio
#             messages=[
#                 {"role": "system", "content": "You are a text analysis assistant."},
#                 {"role": "user", "content": f"Analyze the following text in this resume and return just the name of the person who this resume is for if you cannot find the person name or some other issues occur print out the filename: {text_to_analyze}"}
#             ]
#         )

#         # Retrieve the model's output
#         model_output = completion.choices[0].message.content
#         return model_output

#     except openai.APIError as e:
#         print(f"Error communicating with the local LLM server: {e}")
#         return None

# def get_text_from_file(file_path):
#     # Get the file extension
#     file_extension = os.path.splitext(file_path)[1].lower()
    
#     # Handle .docx files
#     if file_extension == '.docx':
#         doc = docx.Document(file_path)
#         full_text = [para.text for para in doc.paragraphs]
#         return '\n'.join(full_text)
    
#     # Handle .pdf files
#     elif file_extension == '.pdf':
#         try:
#             with pdfplumber.open(file_path) as pdf:
#                 full_text = []
#                 for page in pdf.pages:
#                     full_text.append(page.extract_text())
#                 return '\n'.join(full_text)
#         except Exception as e:
#             print(f"Error reading PDF file {file_path}: {e}")
#             return None
    
    # Return None for unsupported file types
    else:
        return None

if __name__ == "__main__":
    # Example usage in your main loop
    folder_path = '/Users/<USER>/Google Drive/My Drive/Moe_Green_Card/EnvScanResumesRaw'
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        resume_text = get_text_from_file(file_path)
        resume_text = f'{filename} {resume_text}'  # Prepend filename to the text for context